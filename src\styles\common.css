/* Import Inter Variable font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@import url('https://rsms.me/inter/inter.css');

:root {
  --font-family: "DM Sans", "Inter", "Segoe UI", "Roboto", "Helvetica Neue", "Arial",
    "sans-serif";
  --color-purple-opacity: 130, 117, 247;
  --color-purple-300: 92, 0, 250;
  --color-purple-200: 82, 11, 240;
  --color-green-500: 51, 192, 142;
  --color-green-400: 128, 212, 89;
  --color-green-300: 14, 217, 9;
  --color-green-200: 225, 246, 225;
  --color-green-100: var(--color-green-500);
  --color-green-5: 14, 217, 9;

  --color-dark-green-300: 10, 182, 121;

  --color-negative-100: 255, 53, 40;
  --color-negative-200: 234, 53, 41;

  --color-orange-300: 255, 156, 40;
  --color-orange-200: 249, 239, 227;
  --color-orange-5: 255, 156, 40;

  --color-grey-100: 248, 248, 248;
  --color-grey-opacity: 9, 8, 31;

  --color-yellow-300: 251, 255, 47;

  --color-white: 255, 255, 255;
  --color-black: 9, 8, 31;
  --color-black-opacity: 1;

  --color-element: var(--color-black);
  --color-surface: var(--color-white);
  --color-brand: var(--color-purple-opacity);
  --color-alert: var(--color-orange-300);
  --color-positive: var(--color-green-300);
  --color-negative: var(--color-negative-100);

  --color-oklch-100: 97% 0.001 286.4;

  --dt-color-element-2: rgba(var(--color-element), 0.02);
  --dt-color-element-5: rgba(var(--color-element), 0.05);
  --dt-color-element-10: rgba(var(--color-element), 0.1);
  --dt-color-element-20: rgba(var(--color-element), 0.2);
  --dt-color-element-50: rgba(var(--color-element), 0.5);
  --dt-color-element-75: rgba(var(--color-element), 0.75);
  --dt-color-element-100: rgba(var(--color-element), 1);

  --dt-color-brand-5: rgba(var(--color-brand), 0.05);
  --dt-color-brand-10: rgba(var(--color-brand), 0.1);
  --dt-color-brand-40: rgba(var(--color-brand), 0.4);
  --dt-color-brand-50: rgba(var(--color-brand), 0.5);
  --dt-color-brand-60: rgba(var(--color-brand), 0.6);
  --dt-color-brand-100: rgba(var(--color-brand), 1);

  --dt-color-surface-0: rgba(var(--color-surface), 0);
  --dt-color-surface-15: rgba(var(--color-surface), 0.15);
  --dt-color-surface-100: rgba(var(--color-surface), 1);

  --dt-color-alert-5: rgba(var(--color-alert), 0.05);
  --dt-color-alert-100: rgba(var(--color-alert), 1);

  --dt-color-positive-5: rgba(var(--color-positive), 0.05);
  --dt-color-positive-100: rgba(var(--color-positive), 1);

  --dt-color-negative-5: rgba(var(--color-negative), 0.05);
  --dt-color-negative-100: rgba(var(--color-negative), 1);

  /* New theme colors with separate namespace */
  --new-accent: #6351FF;
  --new-button: #EFEFF3;
  --new-hover: #D2D1E0;
  --new-accent-hover: #3F29FF;
  --new-light: #FFFFFF;
  --new-dark: #0F0942;
  --new-stroke: #DFDEED;
  --color-new-grey: 118, 114, 149;
  --color-accent: 99, 81, 255;        /* Keep the same in dark mode */
  --color-button:  239, 239, 243; /* Darker button in dark mode */
  --color-hover: 210, 209, 224;          /* Darker hover in dark mode */
  --color-accent-hover: 63, 41, 255;  /* Keep the same in dark mode */
  --color-light: 9, 8, 31;            /* Swap light/dark in dark mode */
  --color-dark: 255, 255, 255;        /* Swap dark/light in dark mode */
  --color-stroke: 50, 50, 80;         /* Darker stroke in dark mode */
}

/* New theme utility classes */
.new-theme {
  /* Text colors */
  --text-accent: var(--new-accent);
  --text-dark: var(--new-dark);
  --text-light: var(--new-light);
  --text-grey: var(--new-grey);
  
  /* Background colors */
  --bg-accent: var(--new-accent);
  --bg-button: var(--new-button);
  --bg-hover: var(--new-hover);
  --bg-light: var(--new-light);
  --bg-dark: var(--new-dark);
  
  /* Border colors */
  --border-accent: var(--new-accent);
  --border-stroke: var(--new-stroke);
  
  /* Hover colors */
  --hover-accent: var(--new-accent-hover);
}

/* Dark mode overrides for new theme */
[data-mode='dark'] .new-theme,
.dark .new-theme {
  --new-button: #30303C;
  --new-hover: #45454F;
  --new-light: #0F0942;
  --new-dark: #FFFFFF;
  --new-stroke: #32324F;
}

/* New theme background colors */
.new-bg-accent { background-color: var(--new-accent); }
.new-bg-button { background-color: var(--new-button); }
.new-bg-hover { background-color: var(--new-hover); }
.new-bg-light { background-color: var(--new-light); }
.new-bg-dark { background-color: var(--new-dark); }

/* New theme text colors */
.new-text-accent { color: var(--new-accent); }
.new-text-dark { color: var(--new-dark); }
.new-text-light { color: var(--new-light); }
.new-text-grey { color: var(--new-grey); }

/* New theme border colors */
.new-border-accent { border-color: var(--new-accent); }
.new-border-stroke { border-color: var(--new-stroke); }

/* New theme hover states */
.new-hover-accent:hover { background-color: var(--new-accent-hover); }
.new-hover-button:hover { background-color: var(--new-hover); }

/* New theme opacity variants */
.new-bg-accent-10 { background-color: color-mix(in srgb, var(--new-accent) 10%, transparent); }
.new-bg-accent-20 { background-color: color-mix(in srgb, var(--new-accent) 20%, transparent); }
.new-bg-accent-50 { background-color: color-mix(in srgb, var(--new-accent) 50%, transparent); }

.new-bg-button-5 { background-color: color-mix(in srgb, var(--new-button) 5%, transparent); }
.new-bg-button-10 { background-color: color-mix(in srgb, var(--new-button) 10%, transparent); }

.new-text-dark-50 { color: color-mix(in srgb, var(--new-dark) 50%, transparent); }
.new-text-dark-75 { color: color-mix(in srgb, var(--new-dark) 75%, transparent); }

.new-border-accent-20 { border-color: color-mix(in srgb, var(--new-accent) 20%, transparent); }

/* New theme component classes */
.new-btn-primary {
  background-color: var(--new-accent);
  color: var(--new-light);
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.new-btn-primary:hover {
  background-color: var(--new-accent-hover);
}

.new-btn-secondary {
  background-color: var(--new-button);
  color: var(--new-dark);
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.new-btn-secondary:hover {
  background-color: var(--new-hover);
}

.new-btn-outline {
  background-color: transparent;
  color: var(--new-dark);
  border: 1px solid var(--new-stroke);
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.new-btn-outline:hover {
  background-color: color-mix(in srgb, var(--new-hover) 10%, transparent);
}

.new-card {
  background-color: var(--new-light);
  border: 1px solid var(--new-stroke);
  border-radius: 0.5rem;
  padding: 1rem;
}

.new-message-user {
  background-color: color-mix(in srgb, var(--new-accent) 10%, transparent);
  border: 1px solid color-mix(in srgb, var(--new-accent) 20%, transparent);
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: var(--new-dark);
}

.new-message-assistant {
  background-color: color-mix(in srgb, var(--new-button) 10%, transparent);
  border: 1px solid var(--new-stroke);
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: var(--new-dark);
}

.new-input {
  background-color: var(--new-light);
  border: 1px solid var(--new-stroke);
  border-radius: 9999px;
  padding: 0.5rem 1rem;
  color: var(--new-dark);
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.new-input:focus {
  border-color: var(--new-accent);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--new-accent) 20%, transparent);
}

body {
  /* never put background color here */
  margin: 0;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

:focus-visible {
  outline: none !important;
}

@layer base {
  a {
    @apply text-purple-300;
  }

  a:hover {
    @apply text-purple-400;
  }
}

.text-error {
  @apply text-negative-100 text-xs min-h-4 leading-4;
}

/* override autofill and onepassword */
input[data-com-onepassword-filled="light"],
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
input:is(
    :-webkit-autofill,
    :autofill,
    :-webkit-autofill:first-line,
    :autofill:first-line
  ) {
  font-family: inherit !important;
  color: inherit !important;
  -webkit-text-fill-color: inherit !important;
  background-color: transparent !important;
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  box-shadow: 0 0 0 30px white inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

.d-h-screen {
  @apply h-dvh;
}

.no-scrollbar {
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 9999px;
}

.scrollbar::-webkit-scrollbar-thumb {
  background: rgba(var(--color-black), 0.15);
  border-radius: 9999px;
  transition: background-color 0.2s ease;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--color-black), 0.25);
}

.scrollbar::-webkit-scrollbar-track {
  background: rgba(var(--color-black), 0.03);
  border-radius: 9999px;
}

/* Dark mode scrollbar styles */
.dark .scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.dark .scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.12) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.dark .scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.03) !important;
  border-radius: 9999px !important;
}

/* Global scrollbar styles - Apply to ALL scrollbars */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 9999px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(var(--color-black), 0.15);
  border-radius: 9999px;
  transition: background-color 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--color-black), 0.25);
}

*::-webkit-scrollbar-track {
  background: rgba(var(--color-black), 0.03);
  border-radius: 9999px;
}

/* Global dark mode scrollbar styles */
.dark *::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.dark *::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.12) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.dark *::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.03) !important;
  border-radius: 9999px !important;
}

.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -moz-appearance: textfield;
}

.bubble-message li {
  line-height: 1.8;
}

/* Hide textarea auto-resize element on mobile */
@media (max-width: 640px) {
  .bubble-input-container::after {
    display: none;
  }
}

/* Color picker optimizations for better drag performance */
.react-colorful,
.chrome-picker,
[class*="chrome-picker"] {
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Optimize color picker saturation and hue areas for smooth dragging */
.react-colorful__saturation,
.react-colorful__hue,
.chrome-picker .saturation-white,
.chrome-picker .saturation-black,
.chrome-picker .hue-horizontal {
  will-change: transform !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
}

/* Prevent text selection and improve performance on color picker pointers */
.react-colorful__pointer,
.chrome-picker .saturation-white > div,
.chrome-picker .hue-horizontal > div {
  will-change: transform !important;
  transform: translateZ(0) !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  pointer-events: auto !important;
}

/* Mobile-specific optimizations for color picker */
@media (max-width: 640px) {
  .chrome-picker {
    max-width: calc(100vw - 32px) !important;
    width: auto !important;
  }

  /* Increase touch target sizes on mobile */
  .chrome-picker .saturation-white > div,
  .chrome-picker .hue-horizontal > div,
  .react-colorful__pointer {
    width: 20px !important;
    height: 20px !important;
    border-width: 3px !important;
  }

  /* Improve visibility of color picker elements on mobile */
  .chrome-picker .saturation-white > div {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 0, 0, 0.3) !important;
  }

  .chrome-picker .hue-horizontal > div {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 0, 0, 0.3) !important;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .chrome-picker .saturation-white > div,
  .chrome-picker .hue-horizontal > div {
    width: 16px !important;
    height: 16px !important;
    border-width: 2px !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chrome-picker .saturation-white,
  .chrome-picker .saturation-black,
  .chrome-picker .hue-horizontal {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}

/* Prevent zoom on double tap for color picker on iOS */
.chrome-picker,
.chrome-picker * {
  touch-action: manipulation !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
}

/* Improve color picker input fields on mobile */
@media (max-width: 640px) {
  .chrome-picker input {
    font-size: 16px !important; /* Prevent zoom on iOS */
    padding: 8px !important;
    border-radius: 4px !important;
    min-height: 44px !important; /* iOS recommended touch target size */
  }

  /* Ensure color picker doesn't get cut off on small screens */
  .chrome-picker {
    transform: scale(0.9) !important;
    transform-origin: top left !important;
  }
}

/* Landscape orientation optimizations for mobile */
@media (max-width: 640px) and (orientation: landscape) {
  .chrome-picker {
    transform: scale(0.8) !important;
    max-height: calc(100vh - 60px) !important;
  }
}

/* MoonIcon2 dark mode support */
.moon-icon-2 {
  --moon-color: var(--moon-color-light);
}

.dark .moon-icon-2 {
  --moon-color: var(--moon-color-dark);
}

/* Brand background utility class */
.bg-brand-100 {
  background-color: var(--dt-color-brand-100);
}

/* Mobile drag and drop optimizations */
@media (max-width: 768px) {
  /* Improve touch targets for drag handles */
  [data-dnd-kit-sortable] {
    touch-action: manipulation;
  }

  /* Ensure drag handles have adequate touch targets */
  .cursor-grab, .cursor-grabbing {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Prevent text selection during drag on mobile */
  [data-dnd-kit-sortable] * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Improve visual feedback during drag */
  [data-dnd-kit-sortable][data-dnd-kit-dragging] {
    opacity: 0.8;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* Pulse animation for recording state */
@keyframes pulse-recording {
  0% {
    box-shadow: 0 0 0 0 rgba(248, 113, 113, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(248, 113, 113, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(248, 113, 113, 0);
  }
}

.pulse-recording {
  animation: pulse-recording 2s infinite;
}
/* Mobile bubble optimizations to prevent zoom and cursor jumping */
@media (max-width: 768px) {
  /* Prevent zoom on input focus in bubble component */
  .bubble textarea,
  .bubble input,
  .bubble-input-container textarea,
  .bubble-input-container input {
    font-size: 16px !important; /* Prevent zoom on iOS */
    -webkit-text-size-adjust: 100% !important;
    touch-action: manipulation !important;
  }

  /* Ensure bubble component has proper touch behavior */
  .bubble {
    touch-action: manipulation !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  /* Prevent zoom on tab buttons */
  .bubble footer button {
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  /* Hide file inputs properly on mobile */
  .bubble input[type="file"] {
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
}


/* Bubble Animation Classes */
.bubble-opening {
  transform-origin: bottom right;
  animation: bubbleOpen 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bubble-closing {
  transform-origin: bottom right;
  animation: bubbleClose 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Bubble Animation Keyframes */
@keyframes bubbleOpen {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bubbleClose {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}
