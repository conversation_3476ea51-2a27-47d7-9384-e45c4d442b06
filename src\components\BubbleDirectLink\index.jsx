import clsx from 'clsx';
import ChatManager from '../Chatbot/ChatManager';
import * as customizationService from '@/services/customization.service';
import { useEffect, useState, useMemo } from 'react';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import DButtonIcon from '../Global/DButtonIcon';
import ResetIcon from '../Global/Icons/ResetIcon';
import DashboardIcon from '../Global/Icons/DashboardIcon';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import { CHATBOTS_WITH_NEW_DESIGN, COMMON_CLASSNAMES, DANTE_THEME_CHAT, SYSTEM_FONTS } from '@/constants';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Transition,
} from '@headlessui/react';
import HomeContent from '../Bubble/HomeContent';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import CloseIcon from '../Global/Icons/CloseIcon';
import { useSearchParams } from 'react-router-dom';
import DToastContainer from '../DToast/DToastContainer';
import DChatPassword from '../DChatPassword';
import { useRef } from 'react';
import generateGoogleFonts from '@/helpers/generateGoogleFonts';
import DFullLogo from '../Global/DLogo/DFullLogo';
import StyleTag from '../StyleTag';
import BubbleLoadingWrapper from '../BubbleLoadingWrapper';
import DLoading from '../DLoading';
import OptionsIcon from '../Global/Icons/OptionsIcon';
import EmailIcon from '../Global/Icons/EmailIcon';
import UpRightIcon from '../Global/Icons/UpRightIcon';
import DModalEmailTransaction from '../DModalEmailTransaction';
import MenuButtonIcon from '../Global/Icons/MenuButtonIcon';

const BubbleDirectLink = ({
  config,
  isInApp = false,
  initialShouldFetchCustomization = false,
  isPreviewMode = false,
}) => {
  const [searchParams] = useSearchParams();
  const { data: customizationData, isLoading: isLoadingCustomization } =
    useDanteApi(
      customizationService.getSharedChatbotCustomizationById,
      [],
      {},
      {
        kb_id: config?.kb_id,
        token: config?.token,
      }
    );

  const timeoutRef = useRef(null);

  const isAbovePreview = useIsAboveBreakpoint('preview');
  const [showPasswordModal, setShowPasswordModal] = useState(
    customizationData?.public === false
  );
  const [loading, setLoading] = useState(true);
  const [showEmailTranscript, setShowEmailTranscript] = useState(false);
  const [notification, setNotification] = useState({ show: false, type: '', message: '' });

  const [cookie, setCookie] = useState(null);

  const resetCurrentConversation = useConversationStore(
    (state) => state.resetCurrentConversation
  );

  const { setChatbotCustomization, chatbotCustomization } =
    useCustomizationStore();

  const timezone = useTimezoneStore((state) => state.timezone);

  useEffect(() => {
    if (
      customizationData &&
      !isLoadingCustomization &&
      Object.keys(customizationData).length > 2
    ) {
      setChatbotCustomization(customizationData);
    }
  }, [customizationData, isLoadingCustomization, setChatbotCustomization]);

  const handleResetConversation = () => {
    resetCurrentConversation();
    window.location.reload();
  };

  const customizationFontFamily = useMemo(() => {
    return generateGoogleFonts(
      chatbotCustomization.font_name || DANTE_THEME_CHAT.font_name
    );
  }, [chatbotCustomization]);

  useEffect(() => {
    setShowPasswordModal(customizationData?.public === false);
  }, [customizationData]);

  useEffect(() => {
    if (isLoadingCustomization && (!config?.kb_id || !config?.token)) {
      setLoading(true);
      clearTimeout(timeoutRef.current);
    } else {
      timeoutRef.current = setTimeout(() => {
        setLoading(false);
      }, 3000);
    }
  }, [isLoadingCustomization, config]);

  const memoizedConfig = useMemo(() => ({
    ...config,
    ...chatbotCustomization,
    id: searchParams.get('kb_id'),
  }), [config, chatbotCustomization, searchParams]);

  return (
    <>
      <Transition show={loading}>
        <div
          className={clsx(
            'transition-all duration-200 data-[closed]:opacity-0 data-[enter]:delay-200',
            'flex flex-col w-full h-full items-center'
          )}
        >
          <DLoading show={true} />
        </div>
      </Transition>
      <Transition show={!loading}>
        <div
          className={clsx(
            {'py-size3': CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id)},
            'embed',
            'flex flex-col w-full h-full items-center',
            'transition-all duration-200 data-[closed]:opacity-0 data-[enter]:delay-200'
          )}
          style={{
            backgroundColor: 'var(--dt-color-surface-100)',
          }}
        >
          <div className="w-full flex flex-col items-end px-size2">
            <DToastContainer />
          </div>
          <StyleTag
            tag="body"
            tempCustomizationData={chatbotCustomization}
            customImport={customizationFontFamily}
          />
          {config?.kb_id !== 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' && <header className={clsx("flex items-center justify-between py-size3 px-size5 sm:px-size2 w-full max-w-screen-xl", {'pt-0 !px-size2': CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id)})}>
            <div className="flex items-center justify-between w-[95px] h-[25px]">
              <Transition show={chatbotCustomization?.embed_logo_url}>
                <div className="transition-all duration-200 data-[closed]:opacity-0 data-[enter]:delay-200">
                  <img
                    src={chatbotCustomization?.embed_logo_url}
                    alt="logo"
                    className={clsx(
                      'w-[95px] h-[25px] object-contain object-left transition-all',
                      COMMON_CLASSNAMES.transition.duration.medium,
                      {
                        'opacity-0': !chatbotCustomization?.embed_logo_url,
                      }
                    )}
                  />
                </div>
              </Transition>
              <Transition show={!chatbotCustomization?.embed_logo_url}>
                <div className="transition-all duration-200 data-[closed]:opacity-0 data-[enter]:delay-200">
                  <DFullLogo size="lg" />
                </div>
              </Transition>
            </div>
            {!CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && <div className="flex items-center justify-between gap-size1 no-scrollbar">
              <DButtonIcon variant="ghost" onClick={handleResetConversation}>
                <ResetIcon width={16} height={16} className={CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) ? 'size-5 font-bold' : ''}/>
              </DButtonIcon>
              {chatbotCustomization?.home_tab_enabled && (
                <Menu>
                  {!CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && <MenuButton className="bg-transparent border border-[--dt-color-element-10] text-[--dt-color-element-100] size-8 flex items-center justify-center rounded-size0">
                    <DashboardIcon width={16} height={16} />
                  </MenuButton>}
                  <MenuItems
                    transition
                    className={clsx(
                      'fixed top-0 right-0 z-50', // fixed position at top right
                      'w-full sm:w-[368px] h-full',
                      'no-scrollbar pb-size3',
                      'bg-[--dt-color-surface-100] border-l border-[--dt-color-element-10] sm:border sm:rounded-size0 pb-size3',
                      'transition duration-200 ease-out data-[closed]:opacity-0'
                    )}
                  >
                    <MenuItem>
                      {({ close }) => (
                        <>
                          <DButtonIcon
                            variant="ghost"
                            className={clsx('size-8 backdrop-blur-lg absolute z-50', {
                              'right-size3 top-size3': !isAbovePreview,
                              'right-0  top-1': isAbovePreview,
                            })}
                            onClick={(e) => {
                              e.stopPropagation();
                              close();
                            }}
                            style={{
                              color: 'var(--dt-color-element-100)',
                              borderColor: 'var(--dt-color-element-2)',
                              backgroundColor: 'var(--dt-color-element-2)',
                            }}
                          >
                            <CloseIcon />
                          </DButtonIcon>
                          <div className="h-full no-scrollbar w-full px-size3 pt-size3" style={{
                            background: 'radial-gradient(243.51% 165.3% at -5% -5%, var(--dt-color-brand-40) 0%, var(--dt-color-surface-100) 40%)',
                          }}>
                            <HomeContent
                              kb_id={config?.kb_id}
                              token={config?.token}
                              hidePoweredByDante={customizationData?.remove_watermark}
                              previous_conversation_enabled={
                                config?.previous_conversation_enabled
                              }
                              setActiveTab={() => close()}
                            />
                          </div>
                        </>
                      )}
                    </MenuItem>
                  </MenuItems>
                </Menu>
              )}
              <Menu>
                <MenuButton className={`bg-transparent border border-[--dt-color-element-10] text-[--dt-color-element-100] size-8 flex items-center justify-center rounded-size0 ${CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) ? 'border-none' : ''}`}>
                {CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) ? <MenuButtonIcon className="hover:text-accent-hover focus:text-accent" /> :  <OptionsIcon width={16} height={16} />}
                </MenuButton>
                <MenuItems
                  transition
                  className="origin-top-right absolute right-0 mt-2 w-56 rounded-size1 bg-[var(--dt-color-surface-100)] shadow-lg border border-[var(--dt-color-element-10)]"
                  anchor="bottom end"
                >
                  {customizationData?.show_email_details && (
                    <MenuItem>
                      <button
                        className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)] w-full border-b border-grey-5"
                        onClick={() => {
                          setShowEmailTranscript(true);
                        }}
                      >
                        <p className="text-base font-regular tracking-tight">
                          Email transcript
                        </p>
                        <EmailIcon className="w-5 h-4"/>
                      </button>
                    </MenuItem>
                  )}
                  {/* <MenuItem>
                    <button
                      className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)] w-full border-b border-grey-5"
                      onClick={handleResetConversation}
                    >
                      <p className="text-base font-regular tracking-tight">Reset chat</p>
                      <ResetIcon />
                    </button>
                  </MenuItem> */}
                  <MenuItem>
                    <button
                      className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)] w-full border-b border-grey-5"
                      onClick={() =>
                        window.open(
                          chatbotCustomization?.terms_of_use_link ||
                            'https://www.dante-ai.com/terms-of-service',
                          '_blank'
                        )
                      }
                    >
                      <p className="text-base font-regular tracking-tight">
                        Terms of use
                      </p>
                      <UpRightIcon />
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button
                      className="flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 hover:bg-[var(--dt-color-element-5)] w-full"
                      onClick={() =>
                        window.open(
                          chatbotCustomization?.privacy_policy_link ||
                            'https://www.dante-ai.com/privacy-policy',
                          '_blank'
                        )
                      }
                    >
                      <p className="text-base font-regular tracking-tight">
                        Privacy policy
                      </p>
                      <UpRightIcon />
                    </button>
                  </MenuItem>
                </MenuItems>
              </Menu>
            </div>}
          </header>}
          <div className="flex flex-col w-full max-w-screen-lg h-full">
            <ChatManager
              config={memoizedConfig}
              isInApp={isInApp}
              initialShouldFetchCustomization={false}
              isPreviewMode={isPreviewMode}
              cookie={cookie}
              setCookie={setCookie}
              setShowEmailTranscript={setShowEmailTranscript}
              showEmailTranscript={showEmailTranscript}
            />
          </div>
          <DModalEmailTransaction
            open={showEmailTranscript}
            onClose={() => setShowEmailTranscript(false)}
            token={config?.token}
            setNotification={setNotification}
            notification={notification}
          />
          {customizationData?.public === false && showPasswordModal && (
            <div className="absolute bottom-0 left-0 right-0 h-full flex flex-col gap-size1 backdrop-blur-sm justify-end">
              <div className="bg-white p-size5 rounded-size1 flex flex-col gap-size1 z-1">
                <DChatPassword
                  kb_id={config?.kb_id}
                  setCookie={setCookie}
                  onUnlockSuccess={() => setShowPasswordModal(false)}
                />
              </div>
            </div>
          )}
        </div>
      </Transition>
    </>
  );
};

export default BubbleDirectLink;
