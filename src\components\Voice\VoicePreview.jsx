import React, { useEffect } from 'react';
import { useVoicePreviewAudioState } from './useVoicePreviewAudioState';
import { useUserStore } from '@/stores/user/userStore';
import BlobAnimation from './BlobAnimation';
import { voiceOptions as staticVoiceOptions } from '../../pages/AIVoiceDemo/config';
import './VoicePreview.css';
import { WS_ENDPOINT_TYPES } from '@/services/websocket/voiceWebSocketUtils';
import useToast from '@/hooks/useToast';

const VoicePreview = ({
  chatbotId,
  voiceId,
  welcomeMessage,
  voiceProvider,
  personalityPrompt,
  authToken,
  userEmail,
  onClose,
  endpointType = WS_ENDPOINT_TYPES.PREVIEW,
  conversationId,
  hideCloseButton = false,
  place = 'voice-preview',
  isButtonDisabled = false,
  onStart, // new prop
  onStop,
}) => {

  const {
    isStartButtonDisabled,
    isStopButtonDisabled,
    isPlaying,
    userAudioLevel,
    speaking,
    smoothedBotLevel,
    startAudio,
    stopAudio,
    setSettings, // We still need setSettings to update from props
    isMuted,
    toggleMute,
    hasReceivedFirstMessage,
    connectionStatus,
    connectionError,
    progressText
  } = useVoicePreviewAudioState(endpointType);

  // Get user auth data from the user store
  const { auth } = useUserStore();
  
  // Toast notifications
  const { addErrorToast } = useToast();

  // Check for connection or microphone errors and show toast
  useEffect(() => {
    if (connectionStatus === 'error' && connectionError) {
      // Handle microphone-specific errors
      if (connectionError.includes('microphone') || 
          connectionError.includes('Microphone') ||
          progressText?.includes('microphone') || 
          progressText?.includes('Microphone')) {
        addErrorToast({
          title: 'Microphone Access Required',
          message: connectionError || progressText || 'Please allow microphone access in your browser settings to use the AI Voice feature.'
        });
      } else {
        // Handle other connection errors
        addErrorToast({
          title: 'Connection Error',
          message: connectionError || progressText || 'Failed to connect to the voice service.'
        });
      }
    }
  }, [connectionStatus, connectionError, progressText, addErrorToast]);

  useEffect(() => {
    if (
      (endpointType === WS_ENDPOINT_TYPES.SHARED || endpointType === WS_ENDPOINT_TYPES.BROWSER) &&
      !isPlaying &&
      place !== 'arabic-demo'
    ) {
      startAudio().catch(error =>
        console.error('Error auto-starting audio:', error)
      );
    }
  }, [
    chatbotId,
    endpointType,
    startAudio
  ]);
  
  // Update settings when props change
  useEffect(() => {
    setSettings(prevSettings => {
      const newSettings = { ...prevSettings };

      // Update KB ID if provided
      if (chatbotId) {
        newSettings.kbId = chatbotId;
      }

      // Update voice ID if provided
      if (voiceId) {
        if (typeof voiceId === 'object' && voiceId.value) {
          newSettings.selectedVoice = voiceId.value;
        } else {
          newSettings.selectedVoice = voiceId;
        }
      }

      // Update welcome message if provided
      if (welcomeMessage !== undefined) {
        newSettings.initialMessage = welcomeMessage || 'Hello from Dante AI, how can I help you today?';
      }

      // Update personality prompt if provided
      if (personalityPrompt !== undefined) {
        newSettings.personality_prompt = personalityPrompt;
      }

      // Update user email if provided
      if (userEmail) {
        newSettings.userEmail = userEmail;
      }

      if(endpointType === WS_ENDPOINT_TYPES.BROWSER || endpointType === WS_ENDPOINT_TYPES.SHARED) {
        newSettings.conversationId = conversationId;
      }
      // Set voice options based on provider
      if (voiceProvider) {
        // Map provider value to the corresponding voice options
        if (voiceProvider === 'elevenlabs') {
          newSettings.voiceOptions = {
            ...staticVoiceOptions,
            // Prioritize ElevenLabs voices
            elevenlabs: staticVoiceOptions.elevenlabs,
            openai: [],
            cartesia: []
          };
        } else if (voiceProvider === 'openai') {
          newSettings.voiceOptions = {
            ...staticVoiceOptions,
            // Prioritize OpenAI voices
            openai: staticVoiceOptions.openai,
            elevenlabs: [],
            cartesia: []
          };
        } else if (voiceProvider === 'cartesia') {
          newSettings.voiceOptions = {
            ...staticVoiceOptions,
            // Prioritize Cartesia voices
            cartesia: staticVoiceOptions.cartesia,
            openai: [],
            elevenlabs: []
          };
        } else {
          // Default to all options
          newSettings.voiceOptions = staticVoiceOptions;
        }
      }

      // Get JWT token from user store
      const userStore = useUserStore.getState();
      const jwtToken = userStore.auth.access_token;

      // newSettings.jwtToken = jwtToken;

      // Use the provided auth token if available, otherwise use the token from the user store
      newSettings.authToken = authToken || jwtToken || 'DanteAIVoiceDemo';
      return newSettings;
    });
  }, [chatbotId, voiceId, welcomeMessage, personalityPrompt, voiceProvider, authToken, userEmail, auth?.access_token, setSettings, conversationId]);

  // Notify parent component of playing state changes if in chatbot-shortcuts
  useEffect(() => {
    if (place === 'chatbot-shortcuts' && 
        window.__chatbotShortcutsVoicePlayingHandler && 
        typeof window.__chatbotShortcutsVoicePlayingHandler === 'function') {
      window.__chatbotShortcutsVoicePlayingHandler(isPlaying);
    }
  }, [isPlaying, place]);

  const isPreviewReady = chatbotId && voiceId;

  return (
    <div className={`voice-preview-container flex flex-col h-full rounded-lg shadow-md overflow-hidden ${place === 'chatbot-shortcuts' ? 'scale-60' : ''} ${place === 'arabic-demo' ? 'size-full' : ''}`}>
      <div className={`flex-grow flex flex-col items-center justify-center  relative voice-ui ${place === 'chatbot-shortcuts' ? 'p-0' : 'p-3 sm:p-4 md:p-6'}`}>
        {!isPreviewReady ? (
          <div className="flex flex-col items-center justify-center p-3 sm:p-4 md:p-6 text-center fade-in">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-purple-100 rounded-full flex items-center justify-center mb-3 sm:mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <h4 className="text-sm sm:text-base font-medium text-white mb-1 sm:mb-2">Complete the form</h4>
            <p className="text-xs sm:text-sm text-gray-300">
              Fill in the required fields to enable the voice preview
            </p>
          </div>
        ) : (
          <div className="w-full flex flex-col items-center">
            {/* Blob Animation - Responsive sizing */}
            <div className="fade-in scale-75 sm:scale-90 md:scale-100">
              <BlobAnimation
                speaking={speaking}
                userAudioLevel={userAudioLevel}
                smoothedBotLevel={smoothedBotLevel}
                isCalling={isPlaying && !hasReceivedFirstMessage && speaking !== 'bot'}
                className={place === 'chatbot-shortcuts' ? 'shortcuts-blob' : ''}
              />
            </div>

            {/* Control buttons - Responsive layout */}
            <div className={`voice-preview-controls flex flex-wrap ${place === 'chatbot-shortcuts' ? '!justify-between px-5' : 'justify-center'} gap-2 sm:gap-3 md:gap-4 bottom-4 sm:bottom-6 md:bottom-8`}>
              {((endpointType !== WS_ENDPOINT_TYPES.SHARED && endpointType !== WS_ENDPOINT_TYPES.BROWSER) || place === 'arabic-demo') && (
                <button
                  className={`voice-preview-button ${isStartButtonDisabled ? 'secondary' : 'primary'} relative group`}
                  disabled={(isStartButtonDisabled && isStopButtonDisabled) || isButtonDisabled}
                  onClick={isStartButtonDisabled ? () => { if (onStop) onStop(); stopAudio(true)} : () => { if (onStart) onStart(); startAudio(); }}
                  aria-label={isStartButtonDisabled && !isStopButtonDisabled ? 'Stop' : 'Call'}
                >
                  {!isStartButtonDisabled && endpointType === WS_ENDPOINT_TYPES.PREVIEW  && (
                    <div className={`absolute  transform -translate-x-1/2 bg-white text-black px-3 py-1 rounded whitespace-nowrap animate-pulse ${place === 'chatbot-shortcuts' ? 'text-[10px] left-[125px] top-[5px]' : 'text-sm left-1/2 -top-14'}`}>
                      Click here to preview <br /> your AI Voice Agent
                      <div className={`absolute ${place === 'chatbot-shortcuts' ? 'left-[-6px] top-1/2 -translate-y-1/2 border-t-[6px] border-t-transparent border-r-[6px] border-r-white border-b-[6px] border-b-transparent' : 'left-1/2 -translate-x-1/2 -bottom-[5px] border-l-[6px] border-l-transparent border-t-[6px] border-t-white border-r-[6px] border-r-transparent'}`}></div>
                    </div>
                  )}
                  {isStartButtonDisabled && !isStopButtonDisabled ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                  )}
                </button>
              )}
              <button
                className={`voice-preview-button ${isMuted ? 'secondary' : 'secondary'}`}
                disabled={isStopButtonDisabled}
                onClick={toggleMute}
                aria-label={isMuted ? 'Unmute Microphone' : 'Mute Microphone'}
              >
                {isMuted ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className={`${place === 'chatbot-shortcuts' ? 'h-4 w-4 sm:h-5 sm:w-5' : 'h-5 w-5 sm:h-6 sm:w-6'}`} viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.586 7.586a2 2 0 102.828 2.828L5.586 7.586zm5.656 5.656a2 2 0 01-2.828-2.828l2.828 2.828z" clipRule="evenodd" />
                    <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 011-1v2a1 1 0 01-1-1zm7 1a1 1 0 01-1 1v2a1 1 0 011-1v-2zm-3-8a7 7 0 00-5.468 11.37l9.838-9.838A7 7 0 0011 2z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                  </svg>
                )}
              </button>

              {/* Close button - only show if hideCloseButton is false */}
              {!hideCloseButton && (
                <button
                  className="voice-preview-button secondary"
                  onClick={() => {
                    onClose();
                    stopAudio();
                  }}
                  aria-label="Close Voice Preview"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VoicePreview;
